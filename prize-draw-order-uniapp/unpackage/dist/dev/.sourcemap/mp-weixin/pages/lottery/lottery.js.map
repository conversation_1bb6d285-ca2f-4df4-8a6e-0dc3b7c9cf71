{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:////Users/<USER>/projects/prize-draw-order/prize-draw-order-uniapp/pages/lottery/lottery.vue?cd49", "webpack:////Users/<USER>/projects/prize-draw-order/prize-draw-order-uniapp/pages/lottery/lottery.vue?754a", "webpack:////Users/<USER>/projects/prize-draw-order/prize-draw-order-uniapp/pages/lottery/lottery.vue?9cd9", "uni-app:///pages/lottery/lottery.vue", "webpack:////Users/<USER>/projects/prize-draw-order/prize-draw-order-uniapp/pages/lottery/lottery.vue?039f", "webpack:////Users/<USER>/projects/prize-draw-order/prize-draw-order-uniapp/pages/lottery/lottery.vue?cccd"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "merchantCode", "tableNumber", "merchantInfo", "merchantConfig", "currentActivity", "prizeList", "remainingDraws", "drawsInfo", "isDrawing", "isLoading", "userOpenid", "hasDrawn", "lotteryResult", "gridItems", "currentIndex", "animationTimer", "animationSpeed", "animationCount", "targetIndex", "lastWinningRecord", "computed", "uiConfig", "console", "primaryColor", "backgroundGradient", "gridContainerBg", "watch", "handler", "uni", "frontColor", "backgroundColor", "immediate", "onLoad", "onUnload", "methods", "initPage", "loadMerchantInfo", "merchantApi", "res", "loadMerchantConfig", "config<PERSON>pi", "loadCurrentActivity", "lotteryApi", "title", "loadRemainingDraws", "loadDrawsInfo", "loadLastWinningRecord", "winningRecords", "checkUserLotteryStatus", "initGrid", "prizes", "prizeName", "prizeType", "probability", "startLottery", "message", "icon", "drawData", "activityId", "userNickname", "userAvatar", "tableId", "result", "item", "startGridAnimation", "clearTimeout", "setTimeout", "animate", "showResult", "duration", "resetGridStyle", "formatTime", "getRemainingText", "handleError", "content", "showCancel", "confirmText", "getFullImageUrl", "adjustColor"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgI;AAChI;AAC2D;AACL;AACsC;;;AAG5F;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,8FAAM;AACR,EAAE,uGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC1DA;AAAA;AAAA;AAAA;AAAq0B,CAAgB,qyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;AC4Fz1B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;MAAA;MACAC;MACAC;MACAC;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAEA;MACAC;IACA;EACA;EAEAC;IACA;IACAC;MACA;MACA;QACA;UACA;QACA;UACAC;UACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;IACA;EACA;EAEAC;IACA;IACAH;MACAI;QACA;UACAC;YACAC;YACAC;UACA;QACA;MACA;MACAC;IACA;EACA;EAEAC;IACA;IACA;IAEA;EACA;EAGAC;IACA;IACA;EACA;EAEAC;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA;gBACAb;;gBAEA;gBACA;gBACA;;gBAEA;gBACAA;gBAAA;gBAAA,OACA;cAAA;gBAEA;gBACAA;gBAAA;gBAAA,OACA;cAAA;gBAEA;gBACAA;gBAAA;gBAAA,OACA;cAAA;gBAEA;gBACAA;gBAAA;gBAAA,OACA;cAAA;gBAEA;gBACAA;gBAAA;gBAAA,OACA;cAAA;gBAEA;gBACA;kBACAA;kBACA;gBACA;kBACAA;gBACA;gBAEAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAA;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAc;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEAC;cAAA;gBAAAC;gBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEAC;cAAA;gBAAAF;gBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAhB;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAmB;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEAC;cAAA;gBAAAJ;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBACA;gBACA;;gBAEA;gBACA;kBACAV;oBACAe;kBACA;gBACA;;gBAEA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGArB;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAsB;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAAA,OAGAF;cAAA;gBAAAJ;gBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAhB;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAuB;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAAA,OAGAH;cAAA;gBAAAJ;gBACA;kBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAhB;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAIAwB;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEAJ;cAAA;gBAAAJ;gBACA;kBACA;kBACAS;oBAAA;kBAAA;kBACA;oBACA;oBACAA;sBAAA;oBAAA;oBACA;oBACAzB;kBACA;oBACA;oBACAA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA0B;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAAA,OAGAN;cAAA;gBAAAJ;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBACAvC;gBACA;gBACA;;gBAEA;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAIAuB;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA2B;MACA;;MAEA;MACA;;MAEA;MACA;MACA;QACAC;UACAC;UACAC;UACAC;QACA;MACA;;MAEA;MACA;QACAH;MACA;;MAEA;MACA;MACA;MACA;MACA;MACA;QACA;UACA;UACA;QACA;UACA;UACA;UACA;YACA;UACA;YACA;cACAC;cACAC;cACAC;YACA;UACA;QACA;MACA;IACA;IAEAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBACAC;gBACA;kBACA;oBACAA;kBACA;oBACAA;kBACA;gBACA;gBACA3B;kBACAe;kBACAa;gBACA;gBAAA;cAAA;gBAAA,IAGA;kBAAA;kBAAA;gBAAA;gBACA5B;kBACAe;kBACAa;gBACA;gBAAA;cAAA;gBAIA;gBACA;gBACA;gBAAA;gBAGAC;kBACAC;kBACAhD;kBACAiD;kBACAC;kBACAC;gBACA;gBAAA;gBAAA,OAEAnB;cAAA;gBAAAJ;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBACAwB,mBAEA;gBACA5C;kBAAA,OACA6C;gBAAA,EACA,EAEA;gBACA;kBACA7C;gBACA;;gBAEA;gBACA;kBACA;kBACA;kBACA;kBACA;kBACA;oBACA;oBACA;sBACA;oBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA,MAGA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA8C;MAAA;MACA;MACA;MACA;MACA;;MAEA;MACA;MACA;;MAEA;MACA;MACA;MAEA;QACA;QACA;QACA;;QAEA;QACA;UACA;QACA;UACA;QACA;QAEA;UACA;UACA;UACAC;UACAC;QACA;UACA;UACA;QACA;MACA;MAEAC;IACA;IAEAC;MACA;MACA;MAEA;QACA;QACAxC;UACAe;UACAa;UACAa;QACA;MACA;QACA;QACA;QACA;UACA1B;QACA;QAEAf;UACAe;UACAa;UACAa;QACA;MACA;IACA;IAMA;IACAC;MACA;MACA;MACA;MACA;QACAL;QACA;MACA;MACA;MACA;MACA;MACA;IACA;IAIAM;MACA;MACA;IACA;IAEAC;MACA;QACA;MACA;;MAEA;MACA;QACA;QACA;MACA;;MAEA;MACA;QACA;MACA;;MAEA;MACA;QACA;MACA;;MAEA;MACA;IACA;IAMAC;MACA;;MAEA;MACA;QACA7C;UACAe;UACA+B;UACAC;UACAC;QACA;MACA;QACAhD;UACAe;UACAa;UACAa;QACA;MACA;IACA;IAEA;IACAQ;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;MACA;MACA;;MAEA;MACA;MACA;MACA;;MAEA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACvnBA;AAAA;AAAA;AAAA;AAA4jD,CAAgB,47CAAG,EAAC,C;;;;;;;;;;;ACAhlD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/lottery/lottery.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/lottery/lottery.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./lottery.vue?vue&type=template&id=557dc19a&scoped=true&\"\nvar renderjs\nimport script from \"./lottery.vue?vue&type=script&lang=js&\"\nexport * from \"./lottery.vue?vue&type=script&lang=js&\"\nimport style0 from \"./lottery.vue?vue&type=style&index=0&id=557dc19a&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"557dc19a\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/lottery/lottery.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./lottery.vue?vue&type=template&id=557dc19a&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 =\n    !_vm.isLoading && _vm.remainingDraws > 0 && _vm.currentActivity\n      ? _vm.__map(_vm.gridItems, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m0 = index === 4 ? _vm.getRemainingText() : null\n          var m1 =\n            !(index === 4) && item && item.prizeImage\n              ? _vm.getFullImageUrl(item.prizeImage)\n              : null\n          return {\n            $orig: $orig,\n            m0: m0,\n            m1: m1,\n          }\n        })\n      : null\n  var m2 =\n    !_vm.isLoading &&\n    _vm.remainingDraws <= 0 &&\n    _vm.currentActivity &&\n    _vm.lastWinningRecord &&\n    _vm.lastWinningRecord.prizeImage\n      ? _vm.getFullImageUrl(_vm.lastWinningRecord.prizeImage)\n      : null\n  var m3 =\n    !_vm.isLoading &&\n    _vm.remainingDraws <= 0 &&\n    _vm.currentActivity &&\n    _vm.lastWinningRecord\n      ? _vm.formatTime(_vm.lastWinningRecord.drawTime)\n      : null\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event, index) {\n      var _temp = arguments[arguments.length - 1].currentTarget.dataset,\n        _temp2 = _temp.eventParams || _temp[\"event-params\"],\n        index = _temp2.index\n      var _temp, _temp2\n      index === 4 ? _vm.startLottery() : null\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        m2: m2,\n        m3: m3,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./lottery.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./lottery.vue?vue&type=script&lang=js&\"", "<template>\n  <scroll-view class=\"lottery-container\" scroll-y=\"true\" enable-back-to-top=\"true\" :scroll-with-animation=\"true\"\n    :style=\"'background:' + backgroundGradient\">\n    <!-- 加载状态 -->\n    <view class=\"loading-container\" v-if=\"isLoading\">\n      <view class=\"loading-icon\">⏳</view>\n      <view class=\"loading-text\">加载中...</view>\n    </view>\n\n    <!-- 商家信息 -->\n    <view class=\"merchant-header\" v-if=\"merchantInfo && !isLoading\">\n      <view class=\"activity-desc\" v-if=\"currentActivity && currentActivity.activityDesc\">{{ currentActivity.activityDesc\n      }}</view>\n    </view>\n\n\n\n    <!-- 主要内容区域（非加载状态时显示） -->\n    <template v-if=\"!isLoading\">\n      <!-- 无活动提示 -->\n      <view class=\"no-activity-tip\" v-if=\"!currentActivity\">\n        <view class=\"tip-icon\">📢</view>\n        <view class=\"tip-title\">暂无抽奖活动</view>\n        <view class=\"tip-desc\">当前没有进行中的抽奖活动，请稍后再试</view>\n      </view>\n\n      <!-- 九宫格抽奖（有剩余次数时显示） -->\n      <view class=\"lottery-grid-container\" v-if=\"remainingDraws > 0 && currentActivity\">\n        <view class=\"grid-wrapper\">\n          <view class=\"lottery-grid\">\n            <view class=\"grid-item\" v-for=\"(item, index) in gridItems\" :key=\"index\"\n              :class=\"{ 'active': currentIndex === index, 'center': index === 4 }\"\n              :style=\"index === 4 ? 'background:' + gridContainerBg : ''\" @click=\"index === 4 ? startLottery() : null\">\n              <view v-if=\"index === 4\" class=\"center-button\">\n                <view class=\"center-text\">{{ isDrawing ? '抽奖中...' : '点击抽奖' }}</view>\n                <view class=\"remaining-text\">{{ getRemainingText() }}</view>\n              </view>\n              <view v-else-if=\"item\" class=\"prize-item\">\n                <view class=\"prize-icon\">\n                  <image v-if=\"item.prizeImage\" :src=\"getFullImageUrl(item.prizeImage)\" class=\"prize-image\"\n                    mode=\"aspectFit\" />\n                  <text v-else class=\"default-icon\">🎁</text>\n                </view>\n                <view class=\"prize-name\">{{ item.prizeName }}</view>\n              </view>\n            </view>\n          </view>\n        </view>\n      </view>\n\n      <!-- 无抽奖次数时显示上次中奖记录（在九宫格位置） -->\n      <view class=\"lottery-grid-container\" v-if=\"remainingDraws <= 0 && currentActivity && lastWinningRecord\">\n        <view class=\"grid-wrapper\">\n          <view class=\"last-winning-display\">\n            <view class=\"last-winning-header\">\n              <view class=\"header-icon\">🏆</view>\n              <view class=\"header-title\">上次中奖记录</view>\n            </view>\n            <view class=\"winning-content\">\n              <view class=\"prize-display\">\n                <view class=\"prize-icon-large\">\n                  <image v-if=\"lastWinningRecord.prizeImage\" :src=\"getFullImageUrl(lastWinningRecord.prizeImage)\"\n                    class=\"prize-image-large\" mode=\"aspectFit\" />\n                  <text v-else class=\"default-icon-large\">🎁</text>\n                </view>\n                <view class=\"prize-info\">\n                  <view class=\"prize-name-large\">{{ lastWinningRecord.prizeName }}</view>\n                  <view class=\"prize-desc\" v-if=\"lastWinningRecord.prizeDesc\">{{ lastWinningRecord.prizeDesc }}</view>\n                  <view class=\"winning-time\">中奖时间：{{ formatTime(lastWinningRecord.drawTime) }}</view>\n                </view>\n              </view>\n            </view>\n          </view>\n        </view>\n      </view>\n\n      <!-- 抽奖规则（只有在有抽奖次数时显示） -->\n      <view class=\"lottery-rules\" v-if=\"remainingDraws > 0 && currentActivity && currentActivity.drawRules\">\n        <view class=\"rules-title\">抽奖规则</view>\n        <view class=\"rules-content\">{{ currentActivity.drawRules }}</view>\n      </view>\n\n\n\n\n\n\n    </template>\n  </scroll-view>\n</template>\n\n<script>\nimport { merchantApi, lotteryApi, configApi, getImageUrl } from '@/utils/api.js'\n\nexport default {\n  data() {\n    return {\n      merchantCode: '',\n      tableNumber: '',\n      merchantInfo: null,\n      merchantConfig: {}, // 商家配置\n      currentActivity: null,\n      prizeList: [],\n      remainingDraws: 0,\n      drawsInfo: null, // 抽奖次数详情\n      isDrawing: false,\n      isLoading: true, // 页面加载状态\n      userOpenid: '',\n      hasDrawn: false,\n      lotteryResult: null,\n      // 九宫格相关数据\n      gridItems: [],\n      currentIndex: -1,\n      animationTimer: null,\n      animationSpeed: 100,\n      animationCount: 0,\n      targetIndex: -1,\n\n      // 上次中奖记录\n      lastWinningRecord: null\n    }\n  },\n\n  computed: {\n    // 解析UI配置\n    uiConfig() {\n      const uiConfigStr = this.merchantConfig.ui_config\n      if (uiConfigStr) {\n        try {\n          return JSON.parse(uiConfigStr)\n        } catch (e) {\n          console.error('UI配置解析失败:', e)\n          return {}\n        }\n      }\n      return {}\n    },\n\n    // 主题色彩\n    primaryColor() {\n      return this.uiConfig.primaryColor || '#667eea'\n    },\n\n    // 背景渐变色\n    backgroundGradient() {\n      const color = this.primaryColor\n      // 生成基于主题色的渐变背景\n      return `linear-gradient(135deg, ${color} 0%, ${this.adjustColor(color, -20)} 100%)`\n    },\n\n    // 九宫格容器背景色\n    gridContainerBg() {\n      return `linear-gradient(135deg, ${this.primaryColor}, ${this.adjustColor(this.primaryColor, -15)})`\n    }\n  },\n\n  watch: {\n    // 监听主题色彩变化，动态设置导航栏颜色\n    primaryColor: {\n      handler(newColor) {\n        if (newColor) {\n          uni.setNavigationBarColor({\n            frontColor: '#ffffff',\n            backgroundColor: newColor\n          })\n        }\n      },\n      immediate: true\n    }\n  },\n\n  onLoad(options) {\n    this.merchantCode = options.merchantCode || 'system'\n    this.tableNumber = options.tableNumber || 'A001'\n\n    this.initPage()\n  },\n\n\n  onUnload() {\n    // 页面卸载时清理资源\n    this.resetGridStyle()\n  },\n\n  methods: {\n    async initPage() {\n      try {\n        this.isLoading = true\n        console.log('开始初始化抽奖页面...')\n\n        // 获取用户openid（实际项目中需要通过微信登录获取）\n        // 为了测试重复抽奖限制，这里使用固定的测试用户ID\n        this.userOpenid = 'test_user_001'\n\n        // 加载商家信息\n        console.log('加载商家信息...')\n        await this.loadMerchantInfo()\n\n        // 加载商家配置\n        console.log('加载商家配置...')\n        await this.loadMerchantConfig()\n\n        // 加载当前活动\n        console.log('加载当前活动...')\n        await this.loadCurrentActivity()\n\n        // 检查用户抽奖状态\n        console.log('检查用户抽奖状态...')\n        await this.checkUserLotteryStatus()\n\n        // 加载抽奖次数详情\n        console.log('加载抽奖次数详情...')\n        await this.loadDrawsInfo()\n\n        // 初始化九宫格（有剩余次数时初始化）\n        if (this.remainingDraws > 0) {\n          console.log('初始化九宫格，剩余次数:', this.remainingDraws)\n          this.initGrid()\n        } else {\n          console.log('无剩余抽奖次数，不初始化九宫格')\n        }\n\n        console.log('页面初始化完成')\n\n      } catch (error) {\n        console.error('页面初始化失败:', error)\n        this.handleError(error)\n      } finally {\n        this.isLoading = false\n      }\n    },\n\n    async loadMerchantInfo() {\n      try {\n        const res = await merchantApi.getMerchantInfo(this.merchantCode)\n        if (res.code === 200) {\n          this.merchantInfo = res.data\n        }\n      } catch (error) {\n        this.handleError(error)\n      }\n    },\n\n    async loadMerchantConfig() {\n      try {\n        const res = await configApi.getAllConfig(this.merchantCode)\n        if (res.code === 200) {\n          this.merchantConfig = res.data || {}\n        }\n      } catch (error) {\n        console.error('获取商家配置失败:', error)\n      }\n    },\n\n    async loadCurrentActivity() {\n      try {\n        const res = await lotteryApi.getCurrentActivity(this.merchantCode)\n        if (res.code === 200 && res.data) {\n          this.currentActivity = res.data\n          this.prizeList = JSON.parse(res.data.prizeConfig || '[]')\n\n          // 动态设置导航栏标题为活动名称\n          if (this.currentActivity.activityName) {\n            uni.setNavigationBarTitle({\n              title: this.currentActivity.activityName\n            })\n          }\n\n          // 获取剩余抽奖次数\n          await this.loadRemainingDraws()\n        }\n      } catch (error) {\n        console.error('获取活动信息失败:', error)\n      }\n    },\n\n    async loadRemainingDraws() {\n      if (!this.currentActivity) return\n\n      try {\n        const res = await lotteryApi.getRemainingDraws(this.currentActivity.activityId, this.userOpenid)\n        if (res.code === 200) {\n          this.remainingDraws = res.data\n        }\n      } catch (error) {\n        console.error('获取剩余次数失败:', error)\n      }\n    },\n\n    async loadDrawsInfo() {\n      if (!this.currentActivity) return\n\n      try {\n        const res = await lotteryApi.getDrawsInfo(this.currentActivity.activityId, this.userOpenid)\n        if (res.code === 200) {\n          this.drawsInfo = res.data\n          this.remainingDraws = res.data.remainingDraws\n        }\n      } catch (error) {\n        console.error('获取抽奖次数详情失败:', error)\n      }\n    },\n\n\n\n    async loadLastWinningRecord() {\n      try {\n        const res = await lotteryApi.getUserRecords(this.userOpenid)\n        if (res.code === 200) {\n          // 查找最近的中奖记录\n          const winningRecords = res.data.filter(record => record.isWinner === '1')\n          if (winningRecords.length > 0) {\n            // 按时间排序，取最新的中奖记录\n            winningRecords.sort((a, b) => new Date(b.drawTime) - new Date(a.drawTime))\n            this.lastWinningRecord = winningRecords[0]\n            console.log('获取到上次中奖记录:', this.lastWinningRecord)\n          } else {\n            this.lastWinningRecord = null\n            console.log('没有找到中奖记录')\n          }\n        }\n      } catch (error) {\n        console.error('获取上次中奖记录失败:', error)\n        this.lastWinningRecord = null\n      }\n    },\n\n    async checkUserLotteryStatus() {\n      if (!this.currentActivity) return\n\n      try {\n        const res = await lotteryApi.getUserLotteryStatus(this.currentActivity.activityId, this.userOpenid)\n        if (res.code === 200) {\n          const data = res.data\n          this.hasDrawn = data.hasDrawn\n          this.remainingDraws = data.remainingDraws\n\n          // 如果用户没有抽奖次数了，获取上次中奖记录用于显示\n          if (data.remainingDraws <= 0) {\n            await this.loadLastWinningRecord()\n          }\n        }\n      } catch (error) {\n        console.error('获取用户抽奖状态失败:', error)\n      }\n    },\n\n    initGrid() {\n      if (this.prizeList.length === 0) return\n\n      // 创建九宫格数据，确保有8个奖品位置（中间是抽奖按钮）\n      this.gridItems = []\n\n      // 如果奖品不足8个，用\"谢谢参与\"填充\n      const prizes = [...this.prizeList]\n      while (prizes.length < 8) {\n        prizes.push({\n          prizeName: '谢谢参与',\n          prizeType: 'thanks',\n          probability: 0\n        })\n      }\n\n      // 如果奖品超过8个，只取前8个\n      if (prizes.length > 8) {\n        prizes.splice(8)\n      }\n\n      // 九宫格布局：\n      // 0  1  2\n      // 7  4  3  (4是中心抽奖按钮)\n      // 6  5  4\n      // 需要创建9个位置，其中index=4是抽奖按钮，其他8个位置放奖品\n      for (let i = 0; i < 9; i++) {\n        if (i === 4) {\n          // 中心位置是抽奖按钮，不需要奖品数据\n          this.gridItems.push(null)\n        } else {\n          // 计算奖品索引：0,1,2,3对应prizes[0,1,2,3]，5,6,7,8对应prizes[4,5,6,7]\n          const prizeIndex = i < 4 ? i : i - 1\n          if (prizeIndex < prizes.length) {\n            this.gridItems.push(prizes[prizeIndex])\n          } else {\n            this.gridItems.push({\n              prizeName: '谢谢参与',\n              prizeType: 'thanks',\n              probability: 0\n            })\n          }\n        }\n      }\n    },\n\n    async startLottery() {\n      if (this.isDrawing) return\n      if (this.remainingDraws <= 0) {\n        let message = '抽奖次数已用完'\n        if (this.drawsInfo) {\n          if (this.drawsInfo.dailyLimit > 0 && this.drawsInfo.dailyRemaining <= 0) {\n            message = '今日抽奖次数已用完'\n          } else if (this.drawsInfo.totalLimit > 0 && this.drawsInfo.totalRemaining <= 0) {\n            message = '总抽奖次数已用完'\n          }\n        }\n        uni.showToast({\n          title: message,\n          icon: 'none'\n        })\n        return\n      }\n      if (!this.currentActivity) {\n        uni.showToast({\n          title: '暂无可参与的活动',\n          icon: 'none'\n        })\n        return\n      }\n\n      this.isDrawing = true\n      // 重置九宫格样式，确保每次抽奖都从干净的状态开始\n      this.resetGridStyle()\n\n      try {\n        const drawData = {\n          activityId: this.currentActivity.activityId,\n          userOpenid: this.userOpenid,\n          userNickname: '用户' + this.userOpenid.slice(-4),\n          userAvatar: '',\n          tableId: null // 如果有桌台信息可以传入\n        }\n\n        const res = await lotteryApi.performDraw(drawData)\n        if (res.code === 200) {\n          const result = res.data\n\n          // 找到中奖奖品在九宫格中的位置\n          let targetIndex = this.gridItems.findIndex(item =>\n            item && item.prizeName === result.prizeName\n          )\n\n          // 如果没找到，默认停在第一个位置（跳过中心位置）\n          if (targetIndex === -1 || targetIndex === 4) {\n            targetIndex = 0\n          }\n\n          // 开始九宫格动画\n          this.startGridAnimation(targetIndex, () => {\n            this.showResult(result)\n            this.isDrawing = false\n            this.lotteryResult = result\n            // 重新获取剩余次数和用户状态\n            this.loadDrawsInfo().then(() => {\n              // 检查抽奖完成后是否还有剩余次数，如果没有则加载上次中奖记录\n              if (this.remainingDraws <= 0) {\n                this.loadLastWinningRecord()\n              }\n            })\n          })\n\n        } else {\n          throw new Error(res.msg || '抽奖失败')\n        }\n      } catch (error) {\n        this.isDrawing = false\n        this.handleError(error)\n      }\n    },\n\n    startGridAnimation(targetIndex, callback) {\n      this.targetIndex = targetIndex\n      this.animationCount = 0\n      this.currentIndex = 0\n      this.animationSpeed = 100\n\n      // 九宫格动画顺序：跳过中心位置(index=4)\n      // 顺序：0,1,2,3,5,6,7,8 (跳过4)\n      const animationOrder = [0, 1, 2, 3, 5, 6, 7, 8]\n\n      // 动画总圈数和最终位置\n      const totalRounds = 3 // 转3圈\n      const totalSteps = totalRounds * 8 + animationOrder.indexOf(targetIndex)\n\n      const animate = () => {\n        const orderIndex = this.animationCount % 8\n        this.currentIndex = animationOrder[orderIndex]\n        this.animationCount++\n\n        // 动态调整速度，最后几步减速\n        if (this.animationCount > totalSteps - 10) {\n          this.animationSpeed = 200\n        } else if (this.animationCount > totalSteps - 20) {\n          this.animationSpeed = 150\n        }\n\n        if (this.animationCount >= totalSteps) {\n          // 动画结束\n          this.currentIndex = targetIndex\n          clearTimeout(this.animationTimer)\n          setTimeout(callback, 500) // 延迟500ms显示结果\n        } else {\n          // 继续动画\n          this.animationTimer = setTimeout(animate, this.animationSpeed)\n        }\n      }\n\n      animate()\n    },\n\n    showResult(result) {\n      const isWinner = result.isWinner === '1'\n      const isThanks = result.prizeType === 'thanks' || result.prizeName === '谢谢惠顾' || result.prizeName === '谢谢参与'\n\n      if (isWinner) {\n        // 如果中奖，显示Toast提示\n        uni.showToast({\n          title: `恭喜中奖：${result.prizeName}`,\n          icon: 'success',\n          duration: 3000\n        })\n      } else {\n        // 如果未中奖，根据类型显示不同提示\n        let title = '谢谢参与，再接再厉！'\n        if (isThanks) {\n          title = '未抽中，谢谢参与！'\n        }\n\n        uni.showToast({\n          title: title,\n          icon: 'none',\n          duration: 2000\n        })\n      }\n    },\n\n\n\n\n\n    // 重置九宫格样式\n    resetGridStyle() {\n      // 清除当前高亮状态\n      this.currentIndex = -1\n      // 清除动画定时器\n      if (this.animationTimer) {\n        clearTimeout(this.animationTimer)\n        this.animationTimer = null\n      }\n      // 重置动画相关状态\n      this.animationCount = 0\n      this.targetIndex = -1\n      this.animationSpeed = 100\n    },\n\n\n\n    formatTime(timeStr) {\n      const date = new Date(timeStr)\n      return `${date.getMonth() + 1}-${date.getDate()} ${date.getHours()}:${date.getMinutes().toString().padStart(2, '0')}`\n    },\n\n    getRemainingText() {\n      if (!this.drawsInfo) {\n        return `剩余${this.remainingDraws}次`\n      }\n\n      // 如果有每日限制和总限制，显示最小的剩余次数\n      if (this.drawsInfo.dailyLimit > 0 && this.drawsInfo.totalLimit > 0) {\n        const minRemaining = Math.min(this.drawsInfo.dailyRemaining, this.drawsInfo.totalRemaining)\n        return `剩余${minRemaining}次`\n      }\n\n      // 如果只有每日限制\n      if (this.drawsInfo.dailyLimit > 0) {\n        return `今日剩余${this.drawsInfo.dailyRemaining}次`\n      }\n\n      // 如果只有总限制\n      if (this.drawsInfo.totalLimit > 0) {\n        return `总剩余${this.drawsInfo.totalRemaining}次`\n      }\n\n      // 如果没有限制\n      return '点击抽奖'\n    },\n\n\n\n\n\n    handleError(error) {\n      let message = error.message || error.msg || '系统异常'\n\n      // 检查是否是商家到期错误\n      if (message.includes('过期') || message.includes('到期')) {\n        uni.showModal({\n          title: '系统提示',\n          content: message,\n          showCancel: false,\n          confirmText: '我知道了'\n        })\n      } else {\n        uni.showToast({\n          title: message,\n          icon: 'none',\n          duration: 3000\n        })\n      }\n    },\n\n    // 获取完整的图片URL\n    getFullImageUrl(imagePath) {\n      return getImageUrl(imagePath)\n    },\n\n    // 颜色调整工具方法\n    adjustColor(color, amount) {\n      // 将十六进制颜色转换为RGB\n      const hex = color.replace('#', '')\n      const r = parseInt(hex.substr(0, 2), 16)\n      const g = parseInt(hex.substr(2, 2), 16)\n      const b = parseInt(hex.substr(4, 2), 16)\n\n      // 调整亮度\n      const newR = Math.max(0, Math.min(255, r + amount))\n      const newG = Math.max(0, Math.min(255, g + amount))\n      const newB = Math.max(0, Math.min(255, b + amount))\n\n      // 转换回十六进制\n      return `#${newR.toString(16).padStart(2, '0')}${newG.toString(16).padStart(2, '0')}${newB.toString(16).padStart(2, '0')}`\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.lottery-container {\n  min-height: 100vh;\n  /* 背景色通过内联样式动态设置 */\n  padding: 40rpx 30rpx;\n  padding-bottom: 80rpx;\n  /* 增加底部间距，确保内容不被遮挡 */\n  box-sizing: border-box;\n}\n\n/* 加载状态样式 */\n.loading-container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  min-height: 60vh;\n  text-align: center;\n\n  .loading-icon {\n    font-size: 80rpx;\n    margin-bottom: 30rpx;\n    animation: rotate 2s linear infinite;\n  }\n\n  .loading-text {\n    font-size: 32rpx;\n    color: rgba(255, 255, 255, 0.8);\n  }\n}\n\n@keyframes rotate {\n  from {\n    transform: rotate(0deg);\n  }\n\n  to {\n    transform: rotate(360deg);\n  }\n}\n\n.merchant-header {\n  text-align: center;\n  margin-bottom: 60rpx;\n\n  .merchant-name {\n    font-size: 36rpx;\n    font-weight: bold;\n    color: #fff;\n    margin-bottom: 10rpx;\n  }\n\n  .activity-desc {\n    font-size: 28rpx;\n    color: rgba(255, 255, 255, 0.8);\n    line-height: 1.6;\n    padding: 0 20rpx;\n  }\n}\n\n\n\n.lottery-grid-container {\n  display: flex;\n  justify-content: center;\n  margin-bottom: 40rpx;\n\n  .grid-wrapper {\n    width: 600rpx;\n    height: 600rpx;\n  }\n\n  .lottery-grid {\n    display: grid;\n    grid-template-columns: repeat(3, 1fr);\n    grid-template-rows: repeat(3, 1fr);\n    gap: 8rpx;\n    width: 100%;\n    height: 100%;\n    background: rgba(255, 255, 255, 0.1);\n    border-radius: 20rpx;\n    padding: 20rpx;\n    box-sizing: border-box;\n  }\n\n  .grid-item {\n    background: rgba(255, 255, 255, 0.9);\n    border-radius: 15rpx;\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    justify-content: center;\n    position: relative;\n    transition: all 0.3s ease;\n    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);\n\n    &.active {\n      background: linear-gradient(135deg, #ff6b6b, #ff8e8e);\n      transform: scale(1.05);\n      box-shadow: 0 8rpx 25rpx rgba(255, 107, 107, 0.4);\n\n      .prize-item {\n        .prize-icon {\n          animation: bounce 0.6s ease-in-out;\n        }\n\n        .prize-name {\n          color: #fff;\n          font-weight: bold;\n        }\n      }\n    }\n\n    &.center {\n      /* 背景色通过内联样式动态设置 */\n      cursor: pointer;\n\n      &:hover {\n        transform: scale(1.02);\n      }\n\n      &:active {\n        transform: scale(0.98);\n      }\n    }\n  }\n\n  .prize-item {\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    justify-content: center;\n    height: 100%;\n    text-align: center;\n    padding: 10rpx;\n\n    .prize-icon {\n      font-size: 48rpx;\n      margin-bottom: 8rpx;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      width: 60rpx;\n      height: 60rpx;\n\n      .prize-image {\n        width: 100%;\n        height: 100%;\n        border-radius: 8rpx;\n      }\n\n      .default-icon {\n        font-size: 48rpx;\n      }\n    }\n\n    .prize-name {\n      font-size: 24rpx;\n      color: #333;\n      line-height: 1.2;\n      word-break: break-all;\n    }\n  }\n\n  .center-button {\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    justify-content: center;\n    height: 100%;\n    color: #fff;\n\n    .center-text {\n      font-size: 28rpx;\n      font-weight: bold;\n      margin-bottom: 8rpx;\n    }\n\n    .remaining-text {\n      font-size: 20rpx;\n      opacity: 0.9;\n    }\n  }\n}\n\n/* 上次中奖记录显示样式 */\n.last-winning-display {\n  background: rgba(255, 255, 255, 0.95);\n  border-radius: 20rpx;\n  padding: 40rpx 30rpx;\n  text-align: center;\n  box-shadow: 0 8rpx 25rpx rgba(0, 0, 0, 0.15);\n  backdrop-filter: blur(10rpx);\n\n  .last-winning-header {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    margin-bottom: 30rpx;\n    gap: 15rpx;\n\n    .header-icon {\n      font-size: 48rpx;\n    }\n\n    .header-title {\n      font-size: 36rpx;\n      font-weight: bold;\n      color: #333;\n    }\n  }\n\n  .winning-content {\n    .prize-display {\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n      gap: 25rpx;\n\n      .prize-icon-large {\n        width: 120rpx;\n        height: 120rpx;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        background: linear-gradient(135deg, #ffd700, #ffed4e);\n        border-radius: 60rpx;\n        box-shadow: 0 8rpx 20rpx rgba(255, 215, 0, 0.3);\n\n        .prize-image-large {\n          width: 100rpx;\n          height: 100rpx;\n          border-radius: 50rpx;\n        }\n\n        .default-icon-large {\n          font-size: 60rpx;\n          color: #fff;\n        }\n      }\n\n      .prize-info {\n        text-align: center;\n\n        .prize-name-large {\n          font-size: 42rpx;\n          font-weight: bold;\n          color: #333;\n          margin-bottom: 15rpx;\n        }\n\n        .prize-desc {\n          font-size: 28rpx;\n          color: #666;\n          margin-bottom: 15rpx;\n          line-height: 1.5;\n        }\n\n        .winning-time {\n          font-size: 26rpx;\n          color: #999;\n          margin-bottom: 15rpx;\n        }\n\n\n      }\n    }\n  }\n}\n\n@keyframes bounce {\n\n  0%,\n  20%,\n  50%,\n  80%,\n  100% {\n    transform: translateY(0);\n  }\n\n  40% {\n    transform: translateY(-10rpx);\n  }\n\n  60% {\n    transform: translateY(-5rpx);\n  }\n}\n\n/* 无活动提示样式 */\n.no-activity-tip {\n  background: rgba(255, 255, 255, 0.9);\n  border-radius: 20rpx;\n  padding: 60rpx 40rpx;\n  margin-bottom: 40rpx;\n  text-align: center;\n\n  .tip-icon {\n    font-size: 80rpx;\n    margin-bottom: 20rpx;\n  }\n\n  .tip-title {\n    font-size: 36rpx;\n    font-weight: bold;\n    color: #333;\n    margin-bottom: 20rpx;\n  }\n\n  .tip-desc {\n    font-size: 28rpx;\n    color: #666;\n    line-height: 1.6;\n  }\n}\n\n/* 抽奖规则样式 */\n.lottery-rules {\n  background: rgba(255, 255, 255, 0.9);\n  border-radius: 20rpx;\n  padding: 40rpx 30rpx;\n  margin-bottom: 40rpx;\n\n  .rules-title {\n    font-size: 32rpx;\n    font-weight: bold;\n    color: #333;\n    margin-bottom: 20rpx;\n    text-align: center;\n  }\n\n  .rules-content {\n    font-size: 28rpx;\n    color: #666;\n    line-height: 1.8;\n    text-align: left;\n    white-space: pre-wrap;\n  }\n}\n\n/* 抽奖次数用完提示样式 */\n.no-draws-tip {\n  background: rgba(255, 255, 255, 0.9);\n  border-radius: 20rpx;\n  padding: 60rpx 40rpx;\n  margin-bottom: 40rpx;\n  text-align: center;\n\n  .tip-icon {\n    font-size: 80rpx;\n    margin-bottom: 20rpx;\n  }\n\n  .tip-title {\n    font-size: 36rpx;\n    font-weight: bold;\n    color: #333;\n    margin-bottom: 20rpx;\n  }\n\n  .tip-desc {\n    font-size: 28rpx;\n    color: #666;\n    line-height: 1.6;\n\n    text {\n      display: block;\n      margin-bottom: 10rpx;\n    }\n  }\n}\n\n/* 抽奖次数信息样式 */\n.draws-info {\n  background: rgba(255, 255, 255, 0.9);\n  border-radius: 20rpx;\n  padding: 30rpx;\n  margin-bottom: 30rpx;\n\n  .info-title {\n    font-size: 32rpx;\n    font-weight: bold;\n    color: #333;\n    margin-bottom: 20rpx;\n    text-align: center;\n  }\n\n  .info-content {\n    display: flex;\n    flex-direction: column;\n    gap: 15rpx;\n  }\n\n  .info-item {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    flex-wrap: wrap;\n    gap: 10rpx;\n\n    .info-label {\n      font-size: 28rpx;\n      color: #666;\n      font-weight: 500;\n    }\n\n    .info-value {\n      font-size: 28rpx;\n      color: #333;\n      font-weight: bold;\n    }\n\n    .info-remaining {\n      font-size: 24rpx;\n      /* 颜色通过内联样式动态设置 */\n      font-weight: 500;\n    }\n  }\n}\n\n.prize-list {\n  background: rgba(255, 255, 255, 0.9);\n  border-radius: 20rpx;\n  padding: 30rpx;\n  margin-bottom: 40rpx;\n\n  .prize-title {\n    font-size: 32rpx;\n    font-weight: bold;\n    color: #333;\n    margin-bottom: 20rpx;\n    text-align: center;\n  }\n\n  .prize-items {\n    display: flex;\n    flex-wrap: wrap;\n    gap: 20rpx;\n  }\n\n  .prize-item {\n    flex: 1;\n    min-width: 200rpx;\n    background: #f8f9fa;\n    border-radius: 10rpx;\n    padding: 20rpx;\n\n    .prize-info {\n      display: flex;\n      align-items: center;\n      gap: 15rpx;\n    }\n\n    .prize-icon-small {\n      width: 40rpx;\n      height: 40rpx;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      flex-shrink: 0;\n\n      .prize-image-small {\n        width: 100%;\n        height: 100%;\n        border-radius: 6rpx;\n      }\n\n      .default-icon-small {\n        font-size: 32rpx;\n      }\n    }\n\n    .prize-details {\n      flex: 1;\n      text-align: left;\n    }\n\n    .prize-name {\n      font-size: 28rpx;\n      color: #333;\n      margin-bottom: 8rpx;\n    }\n\n    .prize-probability {\n      font-size: 24rpx;\n      color: #666;\n    }\n  }\n}\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./lottery.vue?vue&type=style&index=0&id=557dc19a&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./lottery.vue?vue&type=style&index=0&id=557dc19a&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754184579311\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}